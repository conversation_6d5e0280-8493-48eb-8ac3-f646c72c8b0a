"use client";

import {
  Line<PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Line,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { BaseChart, ChartLoading, ChartEmpty } from "./BaseChart";

interface LineChartData {
  name: string;
  [key: string]: string | number;
}

interface LineConfig {
  dataKey: string;
  stroke: string;
  name: string;
  strokeWidth?: number;
  dot?: boolean;
  activeDot?: { r: number };
}

interface LineChartProps {
  title: string;
  description?: string;
  data: LineChartData[];
  lines: LineConfig[];
  isLoading?: boolean;
  error?: string;
  height?: number;
  className?: string;
  formatValue?: (value: number) => string;
  showLegend?: boolean;
  showGrid?: boolean;
}

const CustomTooltip = ({ 
  active, 
  payload, 
  label,
  formatValue 
}: {
  active?: boolean;
  payload?: any[];
  label?: string;
  formatValue?: (value: number) => string;
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-2 border rounded shadow-sm">
        <p className="font-medium">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {formatValue ? formatValue(entry.value) : entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export function LineChart({
  title,
  description,
  data,
  lines,
  isLoading = false,
  error,
  height = 350,
  className = "",
  formatValue,
  showLegend = true,
  showGrid = true,
}: LineChartProps) {
  const renderChart = () => {
    if (isLoading) return <ChartLoading height={height} />;
    if (error) return <ChartEmpty height={height} message={error} />;
    if (!data || data.length === 0) return <ChartEmpty height={height} />;

    return (
      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <RechartsLineChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis 
              dataKey="name"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis 
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={formatValue}
            />
            <Tooltip content={<CustomTooltip formatValue={formatValue} />} />
            {showLegend && <Legend />}
            {lines.map((line) => (
              <Line
                key={line.dataKey}
                type="monotone"
                dataKey={line.dataKey}
                stroke={line.stroke}
                name={line.name}
                strokeWidth={line.strokeWidth || 2}
                dot={line.dot !== false}
                activeDot={line.activeDot || { r: 8 }}
              />
            ))}
          </RechartsLineChart>
        </ResponsiveContainer>
      </div>
    );
  };

  return (
    <BaseChart
      title={title}
      description={description}
      className={className}
      height={height}
    >
      {renderChart()}
    </BaseChart>
  );
}

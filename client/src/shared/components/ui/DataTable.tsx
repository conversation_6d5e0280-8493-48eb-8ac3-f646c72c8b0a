"use client";

import { ReactNode } from "react";
import { Loader2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface Column<T> {
  key: keyof T | string;
  header: string;
  render?: (item: T, value: any) => ReactNode;
  className?: string;
}

interface DataTableProps<T> {
  title?: string;
  description?: string;
  data: T[];
  columns: Column<T>[];
  isLoading?: boolean;
  error?: string;
  emptyMessage?: string;
  onRowClick?: (item: T) => void;
  actions?: (item: T) => ReactNode;
  className?: string;
  loadingRows?: number;
}

export function DataTable<T extends Record<string, any>>({
  title,
  description,
  data,
  columns,
  isLoading = false,
  error,
  emptyMessage = "No data found",
  onRowClick,
  actions,
  className = "",
  loadingRows = 3,
}: DataTableProps<T>) {
  const renderLoadingState = () => (
    <div className="rounded-md border overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50 hover:bg-muted/50">
            {columns.map((column, index) => (
              <TableHead key={index} className={`font-medium ${column.className || ""}`}>
                {column.header}
              </TableHead>
            ))}
            {actions && <TableHead className="font-medium">Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: loadingRows }).map((_, i) => (
            <TableRow key={i} className="hover:bg-muted/30">
              {columns.map((_, colIndex) => (
                <TableCell key={colIndex}>
                  <Skeleton className="h-6 w-20" />
                </TableCell>
              ))}
              {actions && (
                <TableCell>
                  <Skeleton className="h-6 w-16" />
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  const renderErrorState = () => (
    <div className="rounded-md border p-4 text-center text-muted-foreground">
      {error || "Failed to load data. Please try again later."}
    </div>
  );

  const renderEmptyState = () => (
    <div className="rounded-md border overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50 hover:bg-muted/50">
            {columns.map((column, index) => (
              <TableHead key={index} className={`font-medium ${column.className || ""}`}>
                {column.header}
              </TableHead>
            ))}
            {actions && <TableHead className="font-medium">Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell
              colSpan={columns.length + (actions ? 1 : 0)}
              className="text-center py-4 text-muted-foreground"
            >
              {emptyMessage}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );

  const getValue = (item: T, key: keyof T | string): any => {
    if (typeof key === "string" && key.includes(".")) {
      return key.split(".").reduce((obj, k) => obj?.[k], item);
    }
    return item[key as keyof T];
  };

  const tableContent = () => {
    if (isLoading) return renderLoadingState();
    if (error) return renderErrorState();
    if (data.length === 0) return renderEmptyState();

    return (
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50 hover:bg-muted/50">
              {columns.map((column, index) => (
                <TableHead key={index} className={`font-medium ${column.className || ""}`}>
                  {column.header}
                </TableHead>
              ))}
              {actions && <TableHead className="font-medium">Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item, index) => (
              <TableRow
                key={index}
                className={`hover:bg-muted/30 ${onRowClick ? "cursor-pointer" : ""}`}
                onClick={() => onRowClick?.(item)}
              >
                {columns.map((column, colIndex) => {
                  const value = getValue(item, column.key);
                  return (
                    <TableCell key={colIndex} className={column.className || ""}>
                      {column.render ? column.render(item, value) : value}
                    </TableCell>
                  );
                })}
                {actions && <TableCell>{actions(item)}</TableCell>}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  if (title || description) {
    return (
      <Card className={`shadow-sm ${className}`}>
        {(title || description) && (
          <CardHeader className="pb-2">
            {title && <CardTitle className="text-lg font-semibold">{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        <CardContent className="p-0">{tableContent()}</CardContent>
      </Card>
    );
  }

  return <div className={className}>{tableContent()}</div>;
}

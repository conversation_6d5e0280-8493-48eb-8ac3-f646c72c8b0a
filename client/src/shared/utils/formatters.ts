import { formatCurrency as libFormatCurrency } from "@/lib/utils";

// Currency formatters
export const formatCurrency = libFormatCurrency;

export const formatCurrencyCompact = (amount: number): string => {
  if (Math.abs(amount) >= 1000000) {
    return `${libFormatCurrency(amount / 1000000)}M`;
  }
  if (Math.abs(amount) >= 1000) {
    return `${libFormatCurrency(amount / 1000)}K`;
  }
  return libFormatCurrency(amount);
};

export const formatCurrencyWithSign = (amount: number): string => {
  const sign = amount >= 0 ? "+" : "";
  return `${sign}${libFormatCurrency(amount)}`;
};

// Percentage formatters
export const formatPercentage = (value: number, decimals: number = 2): string => {
  return `${value.toFixed(decimals)}%`;
};

export const formatPercentageWithSign = (value: number, decimals: number = 2): string => {
  const sign = value >= 0 ? "+" : "";
  return `${sign}${value.toFixed(decimals)}%`;
};

// Number formatters
export const formatNumber = (value: number): string => {
  return value.toLocaleString();
};

export const formatNumberCompact = (value: number): string => {
  if (Math.abs(value) >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (Math.abs(value) >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
};

// Date formatters
export const formatDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export const formatDateShort = (date: string | Date): string => {
  return new Date(date).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

export const formatDateLong = (date: string | Date): string => {
  return new Date(date).toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export const formatDateTime = (date: string | Date): string => {
  return new Date(date).toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export const formatRelativeTime = (date: string | Date): string => {
  const now = new Date();
  const targetDate = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return "Just now";
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks > 1 ? "s" : ""} ago`;
  }

  return formatDate(date);
};

// Text formatters
export const formatTitle = (text: string): string => {
  return text
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

export const formatCamelCase = (text: string): string => {
  return text
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
};

export const formatSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "");
};

// Investment specific formatters
export const formatReturn = (value: number): string => {
  const sign = value >= 0 ? "+" : "";
  const color = value >= 0 ? "text-emerald-600" : "text-rose-600";
  return `${sign}${formatCurrency(value)}`;
};

export const formatReturnPercentage = (value: number): string => {
  const sign = value >= 0 ? "+" : "";
  return `${sign}${formatPercentage(value)}`;
};

// Expense specific formatters
export const formatExpenseAmount = (amount: number, type: string): string => {
  const isIncome = type === "INCOME";
  const isExpense = type === "EXPENSE";
  const prefix = isIncome ? "+" : isExpense ? "-" : "";
  return `${prefix}${formatCurrency(Math.abs(amount))}`;
};

export const getAmountColorClass = (amount: number, type: string): string => {
  const isIncome = type === "INCOME";
  const isExpense = type === "EXPENSE";
  
  if (isIncome) return "text-emerald-600";
  if (isExpense) return "text-rose-600";
  return "text-amber-600";
};

// Utility formatters
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
};

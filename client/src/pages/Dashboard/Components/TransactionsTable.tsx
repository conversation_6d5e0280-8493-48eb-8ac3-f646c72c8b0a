"use client";

import { useState, useEffect } from "react";
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CreditCardIcon,
  DollarSignIcon,
  MoreHorizontalIcon,
  PencilIcon,
  TrashIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  EyeIcon,
  CalendarIcon,
  ClipboardIcon,
  FileTextIcon,
  TagIcon,
  ChevronDownIcon,
  FilterIcon,
  XIcon,
} from "lucide-react";
import { useExpense } from "@/contexts/ExpenseContext";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  EditTransactionSheet,
  EditTransactionSheetProps,
} from "@/components/edit-transaction-sheet";
import { motion, AnimatePresence } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import {
  deleteExpense,
  Expense,
  EXPENSE_MAP,
  EXPENSE_TYPE,
} from "@/services/expense.service";
import { toast, useToast } from "@/components/ui/use-toast";
import {
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
  SheetClose,
  Sheet,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
} from "@radix-ui/react-select";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Checkbox } from "@radix-ui/react-checkbox";
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from "@radix-ui/react-collapsible";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@radix-ui/react-popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";

const typeIcons = {
  income: <ArrowUpIcon className="h-4 w-4 text-emerald-500" />,
  expense: <ArrowDownIcon className="h-4 w-4 text-rose-500" />,
  debt_bought: <TrendingDownIcon className="h-4 w-4 text-amber-500" />,
  debt_given: <TrendingUpIcon className="h-4 w-4 text-amber-500" />,
  investment: <DollarSignIcon className="h-4 w-4 text-blue-500" />,
  tax_paid: <CreditCardIcon className="h-4 w-4 text-purple-500" />,
};

const typeColors = {
  income: "bg-emerald-100 text-emerald-800",
  expense: "bg-rose-100 text-rose-800",
  debt_bought: "bg-amber-100 text-amber-800",
  debt_given: "bg-amber-100 text-amber-800",
  investment: "bg-blue-100 text-blue-800",
  tax_paid: "bg-purple-100 text-purple-800",
};

const categoryIcons = {
  Income: "💰",
  Housing: "🏠",
  Food: "🍔",
  Transportation: "🚗",
  Entertainment: "🎬",
  Utilities: "💡",
  Debt: "💳",
  Investment: "📈",
  Tax: "📝",
  Other: "📦",
};

interface TransactionsTableProps {
  filters: {
    type: string;
    categories: string[];
    dateRange: {
      from: Date | undefined;
      to: Date | undefined;
    };
    searchQuery: string;
    sortBy: string;
    sortDirection: "asc" | "desc";
    ignoreDate: boolean;
  };
  transactions: Expense[];
  isLoading: boolean;
}

export default function TransactionsTable({
  filters,
  transactions,
  isLoading,
}: TransactionsTableProps) {
  const { refreshData } = useExpense();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedTransaction, setSelectedTransaction] =
    useState<EditTransactionSheetProps["transaction"]>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const { toast } = useToast();
  const itemsPerPage = 10;

  // Pagination
  const totalPages = Math.ceil(transactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTransactions = transactions.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const handleViewDetails = (transaction: Expense) => {
    setSelectedTransaction({
      id: transaction._id,
      type: transaction.type,
      name: transaction.name || "",
      category: transaction.category,
      amount: transaction.amount,
      date: transaction.eventDate
        ? new Date(transaction.eventDate).toISOString().split("T")[0]
        : "",
      note: transaction.note || "",
    });
    setIsDetailsOpen(true);
  };

  const handleEdit = (transaction: Expense) => {
    setSelectedTransaction({
      id: transaction._id,
      type: transaction.type,
      name: transaction.name || "",
      category: transaction.category,
      amount: transaction.amount,
      date: transaction.eventDate
        ? new Date(transaction.eventDate).toISOString().split("T")[0]
        : "",
      note: transaction.note || "",
    });
    setIsEditOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteExpense(id);

      // Refresh data in the context to trigger re-fetch
      refreshData();

      toast({
        title: "Success",
        description: "Transaction deleted successfully",
      });
    } catch (err) {
      console.error("Failed to delete transaction:", err);
      toast({
        title: "Error",
        description: "Failed to delete transaction. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return <TransactionTableSkeleton />;
  }

  return (
    <div className="space-y-4">
      {transactions.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex h-[300px] w-full items-center justify-center rounded-xl border border-dashed p-8"
        >
          <div className="flex flex-col items-center justify-center space-y-3 text-center">
            <div className="rounded-full bg-muted p-3">
              <SearchIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold">No transactions found</h3>
            <p className="text-sm text-muted-foreground max-w-[250px]">
              Try adjusting your filters or add a new transaction to get
              started.
            </p>
          </div>
        </motion.div>
      ) : (
        <>
          {/* Desktop view - Table */}
          <div className="hidden md:block rounded-xl border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50 hover:bg-muted/50">
                  <TableHead>Type</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <AnimatePresence>
                  {paginatedTransactions.map((transaction, index) => (
                    <motion.tr
                      key={transaction._id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0 }}
                      transition={{ delay: index * 0.05, duration: 0.2 }}
                      className="border-b hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleViewDetails(transaction)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {typeIcons[transaction.type]}
                          <Badge
                            variant="outline"
                            className={typeColors[transaction.type]}
                          >
                            {transaction.type.replace("_", " ")}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {transaction.name || "Unnamed"}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">
                            {categoryIcons[transaction.category] || "📦"}
                          </span>
                          {transaction.category}
                        </div>
                      </TableCell>
                      <TableCell>
                        {transaction.eventDate
                          ? new Date(transaction.eventDate).toLocaleDateString()
                          : new Date(
                              transaction.createdAt || ""
                            ).toLocaleDateString()}
                      </TableCell>
                      <TableCell
                        className={`text-right font-medium ${
                          transaction.type === EXPENSE_TYPE.INCOME
                            ? "text-emerald-600"
                            : transaction.type === EXPENSE_TYPE.EXPENSE
                            ? "text-rose-600"
                            : ""
                        }`}
                      >
                        {transaction.type === EXPENSE_TYPE.INCOME
                          ? "+"
                          : transaction.type === EXPENSE_TYPE.EXPENSE
                          ? "-"
                          : ""}
                        ${Math.abs(transaction.amount).toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger
                            asChild
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontalIcon className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewDetails(transaction);
                              }}
                            >
                              <EyeIcon className="mr-2 h-4 w-4" />
                              View details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(transaction);
                              }}
                            >
                              <PencilIcon className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(transaction._id || "");
                              }}
                            >
                              <TrashIcon className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </TableBody>
            </Table>
          </div>

          {/* Mobile view - Cards */}
          <div className="grid gap-3 md:hidden">
            <AnimatePresence>
              {paginatedTransactions.map((transaction, index) => (
                <motion.div
                  key={transaction._id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ delay: index * 0.05, duration: 0.2 }}
                >
                  <div className="rounded-xl border bg-card shadow-sm overflow-hidden">
                    <div className="p-4 flex items-center justify-between border-b">
                      <div className="flex items-center gap-2">
                        {typeIcons[transaction.type]}
                        <Badge
                          variant="outline"
                          className={typeColors[transaction.type]}
                        >
                          {transaction.type.replace("_", " ")}
                        </Badge>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <MoreHorizontalIcon className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewDetails(transaction)}
                          >
                            <EyeIcon className="mr-2 h-4 w-4" />
                            View details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEdit(transaction)}
                          >
                            <PencilIcon className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleDelete(transaction._id || "")}
                          >
                            <TrashIcon className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <div
                      className="p-4"
                      onClick={() => handleViewDetails(transaction)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">
                          {transaction.name || "Unnamed"}
                        </h3>
                        <div
                          className={`font-medium ${
                            transaction.type === EXPENSE_TYPE.INCOME
                              ? "text-emerald-600"
                              : transaction.type === EXPENSE_TYPE.EXPENSE
                              ? "text-rose-600"
                              : ""
                          }`}
                        >
                          {transaction.type === EXPENSE_TYPE.INCOME
                            ? "+"
                            : transaction.type === EXPENSE_TYPE.EXPENSE
                            ? "-"
                            : ""}
                          ${Math.abs(transaction.amount).toFixed(2)}
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <span>
                            {categoryIcons[transaction.category] || "📦"}
                          </span>
                          <span>{transaction.category}</span>
                        </div>
                        <div>
                          {transaction.eventDate
                            ? new Date(
                                transaction.eventDate
                              ).toLocaleDateString()
                            : new Date(
                                transaction.createdAt || ""
                              ).toLocaleDateString()}
                        </div>
                      </div>
                      {transaction.note && (
                        <p className="mt-2 text-sm text-muted-foreground line-clamp-1">
                          {transaction.note}
                        </p>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </>
      )}

      {totalPages > 1 && (
        <div className="flex items-center justify-center mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  size="icon"
                  className={
                    currentPage === 1
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>

              {/* Mobile pagination - just show current/total */}
              <div className="md:hidden flex items-center">
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
              </div>

              {/* Desktop pagination - show page numbers */}
              <div className="hidden md:flex">
                {Array.from({ length: Math.min(totalPages, 5) }).map((_, i) => {
                  let pageNumber = i + 1;
                  if (totalPages > 5 && currentPage > 3) {
                    pageNumber = currentPage - 3 + i;
                    if (pageNumber > totalPages) {
                      return null;
                    }
                  }
                  return (
                    <PaginationItem key={i}>
                      <PaginationLink
                        size="icon"
                        onClick={() => setCurrentPage(pageNumber)}
                        isActive={currentPage === pageNumber}
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
                {totalPages > 5 && currentPage < totalPages - 1 && (
                  <PaginationItem>
                    <PaginationLink
                      size="icon"
                      onClick={() => setCurrentPage(totalPages)}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )}
              </div>

              <PaginationItem>
                <PaginationNext
                  size="icon"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {selectedTransaction && (
        <>
          <TransactionDetailsSheet
            transaction={selectedTransaction}
            open={isDetailsOpen}
            onOpenChange={setIsDetailsOpen}
            onEdit={() => {
              setIsDetailsOpen(false);
              setIsEditOpen(true);
            }}
          />
          <EditTransactionSheet
            transaction={selectedTransaction}
            open={isEditOpen}
            onOpenChange={setIsEditOpen}
          />
        </>
      )}
    </div>
  );
}

function TransactionTableSkeleton() {
  return (
    <div className="space-y-4">
      {/* Desktop skeleton */}
      <div className="hidden md:block rounded-xl border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50 hover:bg-muted/50">
              <TableHead>Type</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Amount</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, i) => (
              <TableRow key={i}>
                <TableCell>
                  <Skeleton className="h-8 w-20" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-32" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-28" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-20 ml-auto" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-8 w-8 rounded-full" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile skeleton */}
      <div className="grid gap-3 md:hidden">
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="rounded-xl border bg-card shadow-sm overflow-hidden"
          >
            <div className="p-4 flex items-center justify-between border-b">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Skeleton className="h-5 w-28" />
                <Skeleton className="h-5 w-20" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function SearchIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="11" cy="11" r="8" />
      <path d="m21 21-4.3-4.3" />
    </svg>
  );
}

interface TransactionDetailsSheetProps {
  transaction: {
    id: string;
    type: string;
    name: string;
    category: string;
    amount: number;
    date: string;
    note: string;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit: () => void;
}

export function TransactionDetailsSheet({
  transaction,
  open,
  onOpenChange,
  onEdit,
}: TransactionDetailsSheetProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: "Transaction ID has been copied to clipboard",
      duration: 2000,
    });
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[500px] w-full overflow-y-auto">
        <SheetHeader className="space-y-1">
          <SheetTitle className="text-2xl">Transaction Details</SheetTitle>
          <SheetDescription>
            View complete information about this transaction
          </SheetDescription>
        </SheetHeader>

        <div className="py-6 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {typeIcons[transaction.type]}
              <Badge
                variant="outline"
                className={`${typeColors[transaction.type]} text-sm px-3 py-1`}
              >
                {transaction.type.replace("_", " ")}
              </Badge>
            </div>
            <div
              className={`text-2xl font-bold ${
                transaction.type === "income"
                  ? "text-emerald-600"
                  : transaction.type === "expense"
                  ? "text-rose-600"
                  : ""
              }`}
            >
              {transaction.type === "income"
                ? "+"
                : transaction.type === "expense"
                ? "-"
                : ""}
              ${transaction.amount.toFixed(2)}
            </div>
          </div>

          <Separator />

          <div className="space-y-5">
            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <FileTextIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Name
                </p>
                <p className="text-lg font-medium">{transaction.name}</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <TagIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Category
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-lg">
                    {categoryIcons[transaction.category]}
                  </span>
                  <p className="text-lg font-medium">{transaction.category}</p>
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <CalendarIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Date
                </p>
                <p className="text-lg font-medium">
                  {new Date(transaction.date).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
            </div>

            {transaction.note && (
              <div className="flex items-start gap-3">
                <div className="bg-muted/60 p-2 rounded-md">
                  <FileTextIcon className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">
                    Note
                  </p>
                  <p className="text-lg">{transaction.note}</p>
                </div>
              </div>
            )}

            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <ClipboardIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1 flex-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Transaction ID
                </p>
                <div className="flex items-center justify-between">
                  <p className="text-sm font-mono">{transaction.id}</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(transaction.id)}
                    className="h-8 px-2"
                  >
                    Copy
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <SheetFooter className="flex flex-col sm:flex-row gap-3">
          <SheetClose asChild>
            <Button variant="outline" className="flex-1">
              Close
            </Button>
          </SheetClose>
          <Button onClick={onEdit} className="flex-1">
            <PencilIcon className="mr-2 h-4 w-4" />
            Edit Transaction
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}

// Available categories
const categories = [
  "Income",
  "Housing",
  "Food",
  "Transportation",
  "Entertainment",
  "Utilities",
  "Debt",
  "Investment",
  "Tax",
  "Other",
];

interface TransactionFiltersProps {
  filters: {
    type: string;
    categories: string[];
    dateRange: {
      from: Date | undefined;
      to: Date | undefined;
    };
    searchQuery: string;
    sortBy: string;
    sortDirection: "asc" | "desc";
    ignoreDate?: boolean;
  };
  setFilters: React.Dispatch<
    React.SetStateAction<{
      type: string;
      categories: string[];
      dateRange: {
        from: Date | undefined;
        to: Date | undefined;
      };
      searchQuery: string;
      sortBy: string;
      sortDirection: "asc" | "desc";
      ignoreDate?: boolean;
    }>
  >;
  hideDateFilter?: boolean;
}

export function TransactionFilters({
  filters,
  setFilters,
  hideDateFilter = false,
}: TransactionFiltersProps) {
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  const handleReset = () => {
    setFilters({
      type: "all",
      categories: [],
      dateRange: {
        from: undefined,
        to: undefined,
      },
      searchQuery: "",
      sortBy: "date",
      sortDirection: "desc",
      ignoreDate: filters.ignoreDate, // Preserve the ignoreDate setting
    });
  };

  const handleCategoryToggle = (category: string) => {
    setFilters((prev) => {
      if (prev.categories.includes(category)) {
        return {
          ...prev,
          categories: prev.categories.filter((c) => c !== category),
        };
      } else {
        return {
          ...prev,
          categories: [...prev.categories, category],
        };
      }
    });
  };

  const handleSortChange = (value: string) => {
    const [sortBy, sortDirection] = value.split("-");
    setFilters((prev) => ({
      ...prev,
      sortBy,
      sortDirection: sortDirection as "asc" | "desc",
    }));
  };

  const getSortLabel = () => {
    switch (`${filters.sortBy}-${filters.sortDirection}`) {
      case "date-desc":
        return "Date (Newest First)";
      case "date-asc":
        return "Date (Oldest First)";
      case "amount-desc":
        return "Amount (High to Low)";
      case "amount-asc":
        return "Amount (Low to High)";
      case "name-asc":
        return "Name (A-Z)";
      case "name-desc":
        return "Name (Z-A)";
      default:
        return "Sort by";
    }
  };

  const hasActiveFilters =
    filters.type !== "all" ||
    filters.categories.length > 0 ||
    (!hideDateFilter && (filters.dateRange.from || filters.dateRange.to)) ||
    filters.searchQuery;

  return (
    <div className="space-y-4">
      <div className="relative">
        <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search transactions..."
          className="pl-10 pr-4 h-11 rounded-lg border-muted-foreground/20"
          value={filters.searchQuery}
          onChange={(e) =>
            setFilters({ ...filters, searchQuery: e.target.value })
          }
        />
      </div>

      <div className="md:hidden flex items-center justify-between gap-2">
        <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2 h-10">
              <FilterIcon className="h-4 w-4" />
              <span>Filters</span>
              {hasActiveFilters && (
                <Badge
                  variant="secondary"
                  className="ml-1 h-5 px-1.5 rounded-full"
                >
                  {(filters.type !== "all" ? 1 : 0) +
                    filters.categories.length +
                    (!hideDateFilter &&
                    (filters.dateRange.from || filters.dateRange.to)
                      ? 1
                      : 0)}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[85vh] rounded-t-xl">
            <SheetHeader className="mb-4">
              <SheetTitle>Filters & Sorting</SheetTitle>
            </SheetHeader>
            <div className="space-y-6 overflow-y-auto max-h-[calc(85vh-80px)] pb-20">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Transaction Type</h3>
                <Select
                  value={filters.type}
                  onValueChange={(value) =>
                    setFilters({ ...filters, type: value })
                  }
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {Object.values(EXPENSE_TYPE).map((type) => (
                      <SelectItem key={type} value={type}>
                        {EXPENSE_MAP[type]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Categories</h3>
                  {filters.categories.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters({ ...filters, categories: [] })}
                      className="h-auto py-1 px-2 text-xs"
                    >
                      Clear all
                    </Button>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-3">
                  {categories.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category}-mobile`}
                        checked={filters.categories.includes(category)}
                        onCheckedChange={() => handleCategoryToggle(category)}
                        className=""
                      />
                      <label
                        htmlFor={`category-${category}-mobile`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {category}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Sort By</h3>
                <Select
                  value={`${filters.sortBy}-${filters.sortDirection}`}
                  onValueChange={handleSortChange}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">
                      Date (Newest First)
                    </SelectItem>
                    <SelectItem value="date-asc">
                      Date (Oldest First)
                    </SelectItem>
                    <SelectItem value="amount-desc">
                      Amount (High to Low)
                    </SelectItem>
                    <SelectItem value="amount-asc">
                      Amount (Low to High)
                    </SelectItem>
                    <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                    <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {!hideDateFilter && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Date Range</h3>
                  <div className="w-full">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="date-range"
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal h-10",
                            !filters.dateRange.from &&
                              !filters.dateRange.to &&
                              "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.dateRange.from && filters.dateRange.to ? (
                            <>
                              {format(filters.dateRange.from, "MMM d, yyyy")} -{" "}
                              {format(filters.dateRange.to, "MMM d, yyyy")}
                            </>
                          ) : filters.dateRange.from ? (
                            <>
                              From:{" "}
                              {format(filters.dateRange.from, "MMM d, yyyy")}
                            </>
                          ) : filters.dateRange.to ? (
                            <>
                              To: {format(filters.dateRange.to, "MMM d, yyyy")}
                            </>
                          ) : (
                            <span>Select date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 shadow-lg"
                        align="start"
                      >
                        <div className="p-3 border-b">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium">Date Range</h4>
                            {(filters.dateRange.from ||
                              filters.dateRange.to) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  setFilters({
                                    ...filters,
                                    dateRange: {
                                      from: undefined,
                                      to: undefined,
                                    },
                                  })
                                }
                                className="h-auto py-1 px-2 text-xs"
                              >
                                Clear
                              </Button>
                            )}
                          </div>
                          <div className="grid grid-cols-2 gap-2 mt-2">
                            <div>
                              <p className="text-xs text-muted-foreground mb-1">
                                From
                              </p>
                              <div className="p-2 border rounded-md text-sm">
                                {filters.dateRange.from
                                  ? format(
                                      filters.dateRange.from,
                                      "MMM d, yyyy"
                                    )
                                  : "Not set"}
                              </div>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground mb-1">
                                To
                              </p>
                              <div className="p-2 border rounded-md text-sm">
                                {filters.dateRange.to
                                  ? format(filters.dateRange.to, "MMM d, yyyy")
                                  : "Not set"}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="p-3">
                          <Calendar
                            mode="range"
                            selected={{
                              from: filters.dateRange.from,
                              to: filters.dateRange.to,
                            }}
                            onSelect={(range) => {
                              if (range) {
                                setFilters({
                                  ...filters,
                                  dateRange: {
                                    from: range.from,
                                    to: range.to,
                                  },
                                });
                              }
                            }}
                            numberOfMonths={1}
                            defaultMonth={filters.dateRange.from || new Date()}
                            fixedWeeks
                            showOutsideDays={false}
                          />
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              )}

              <div className="pt-4 sticky bottom-0 bg-background pb-4 mt-8">
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    className="flex-1"
                  >
                    Reset All
                  </Button>
                  <Button
                    onClick={() => setIsFiltersOpen(false)}
                    className="flex-1"
                  >
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          </SheetContent>
        </Sheet>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2 h-10">
              {filters.sortDirection === "asc" ? (
                <ArrowUpIcon className="h-4 w-4" />
              ) : (
                <ArrowDownIcon className="h-4 w-4" />
              )}
              <span>Sort</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Sort by</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuRadioGroup
              value={`${filters.sortBy}-${filters.sortDirection}`}
              onValueChange={handleSortChange}
            >
              <DropdownMenuRadioItem value="date-desc">
                Date (Newest First)
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="date-asc">
                Date (Oldest First)
              </DropdownMenuRadioItem>
              <DropdownMenuSeparator />
              <DropdownMenuRadioItem value="amount-desc">
                Amount (High to Low)
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="amount-asc">
                Amount (Low to High)
              </DropdownMenuRadioItem>
              <DropdownMenuSeparator />
              <DropdownMenuRadioItem value="name-asc">
                Name (A-Z)
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="name-desc">
                Name (Z-A)
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Desktop filters */}
      <div className="hidden md:block">
        <Collapsible>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 gap-1">
                  <FilterIcon className="h-3.5 w-3.5" />
                  <span>Filters</span>
                  {hasActiveFilters && (
                    <Badge
                      variant="secondary"
                      className="ml-1 h-5 px-1.5 rounded-full"
                    >
                      {(filters.type !== "all" ? 1 : 0) +
                        filters.categories.length +
                        (!hideDateFilter &&
                        (filters.dateRange.from || filters.dateRange.to)
                          ? 1
                          : 0)}
                    </Badge>
                  )}
                </Button>
              </CollapsibleTrigger>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 gap-1">
                    {filters.sortDirection === "asc" ? (
                      <ArrowUpIcon className="h-3.5 w-3.5" />
                    ) : (
                      <ArrowDownIcon className="h-3.5 w-3.5" />
                    )}
                    <span>{getSortLabel()}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={`${filters.sortBy}-${filters.sortDirection}`}
                    onValueChange={handleSortChange}
                  >
                    <DropdownMenuRadioItem value="date-desc">
                      Date (Newest First)
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="date-asc">
                      Date (Oldest First)
                    </DropdownMenuRadioItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuRadioItem value="amount-desc">
                      Amount (High to Low)
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="amount-asc">
                      Amount (Low to High)
                    </DropdownMenuRadioItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuRadioItem value="name-asc">
                      Name (A-Z)
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="name-desc">
                      Name (Z-A)
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                  className="h-9 text-muted-foreground hover:text-foreground gap-1"
                >
                  <XIcon className="h-3.5 w-3.5" />
                  <span>Clear all</span>
                </Button>
              )}
            </div>

            {/* Active filters display */}
            {hasActiveFilters && (
              <div className="flex flex-wrap items-center gap-2">
                {filters.type !== "all" && (
                  <Badge variant="outline" className="h-6 gap-1 px-2">
                    Type: {filters.type.replace("_", " ")}
                    <XIcon
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setFilters({ ...filters, type: "all" })}
                    />
                  </Badge>
                )}

                {/* {!hideDateFilter &&
                  (filters.dateRange.from || filters.dateRange.to) && (
                    <Badge variant="outline" className="h-6 gap-1 px-2">
                      {filters.dateRange.from && filters.dateRange.to ? (
                        <>
                          Date: {format(filters.dateRange.from, "MMM d")} -{" "}
                          {format(filters.dateRange.to, "MMM d, yyyy")}
                        </>
                      ) : filters.dateRange.from ? (
                        <>
                          From: {format(filters.dateRange.from, "MMM d, yyyy")}
                        </>
                      ) : (
                        <>To: {format(filters.dateRange.to, "MMM d, yyyy")}</>
                      )}
                      <XIcon
                        className="h-3 w-3 cursor-pointer ml-1"
                        onClick={() =>
                          setFilters({
                            ...filters,
                            dateRange: { from: undefined, to: undefined },
                          })
                        }
                      />
                    </Badge>
                  )} */}

                {filters.categories.length > 0 && (
                  <Badge variant="outline" className="h-6 gap-1 px-2">
                    Categories: {filters.categories.length}
                    <XIcon
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setFilters({ ...filters, categories: [] })}
                    />
                  </Badge>
                )}
              </div>
            )}
          </div>

          <CollapsibleContent className="mt-4 space-y-4">
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
            >
              <div className="space-y-2">
                <p className="text-sm font-medium">Transaction Type</p>
                <Select
                  value={filters.type}
                  onValueChange={(value) =>
                    setFilters({ ...filters, type: value })
                  }
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="income">Income</SelectItem>
                    <SelectItem value="expense">Expense</SelectItem>
                    <SelectItem value="debt_bought">Debt Bought</SelectItem>
                    <SelectItem value="debt_given">Debt Given</SelectItem>
                    <SelectItem value="investment">Investment</SelectItem>
                    <SelectItem value="tax_paid">Tax Paid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">Categories</p>
                  {filters.categories.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters({ ...filters, categories: [] })}
                      className="h-auto py-1 px-2 text-xs"
                    >
                      Clear
                    </Button>
                  )}
                </div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between h-10"
                    >
                      {filters.categories.length > 0 ? (
                        <span>{filters.categories.length} selected</span>
                      ) : (
                        <span className="text-muted-foreground">
                          Select categories
                        </span>
                      )}
                      <ChevronDownIcon className="h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-[220px] p-0 shadow-lg"
                    align="start"
                  >
                    <div className="p-2 max-h-[300px] overflow-y-auto">
                      {categories.map((category) => (
                        <div
                          key={category}
                          className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded-md"
                        >
                          <Checkbox
                            id={`category-${category}`}
                            checked={filters.categories.includes(category)}
                            onCheckedChange={() =>
                              handleCategoryToggle(category)
                            }
                          />
                          <label
                            htmlFor={`category-${category}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                          >
                            {category}
                          </label>
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
                {filters.categories.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {filters.categories.map((category) => (
                      <Badge
                        key={category}
                        variant="secondary"
                        className="text-xs"
                      >
                        {category}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}

"use client";

import { useState } from "react";
import { PlusIcon, CalendarIcon } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { AddTransactionSheet } from "@/components/add-transaction-sheet";
import { ExpenseProvider, useExpense } from "@/contexts/ExpenseContext";
import { BasePage } from "@/shared/components";
import Transactions from "./Components/Transactions";
import DashboardOverview from "./Components/Overview";

export default function DashboardPage() {
  return (
    <ExpenseProvider>
      <DashboardContent />
    </ExpenseProvider>
  );
}

function DashboardContent() {
  const [isAddTransactionOpen, setIsAddTransactionOpen] = useState(false);
  const {
    loading,
    error,
    refreshData,
    dateRange,
    setDateRange,
    ignoreDate,
    setIgnoreDate,
  } = useExpense();

  // Create header actions
  const headerActions = (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="h-9 border-dashed">
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange.from && dateRange.to ? (
              <>
                {format(dateRange.from, "MMM d, yyyy")} -{" "}
                {format(dateRange.to, "MMM d, yyyy")}
              </>
            ) : (
              <span>Select date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          <Calendar
            mode="range"
            selected={{
              from: dateRange.from,
              to: dateRange.to,
            }}
            onSelect={(range) => {
              if (range) {
                setDateRange({
                  from: range.from,
                  to: range.to,
                });
              }
            }}
            numberOfMonths={1}
            defaultMonth={dateRange.from}
          />
        </PopoverContent>
      </Popover>
      <Button
        onClick={() => setIsAddTransactionOpen(true)}
        className="hidden md:flex"
      >
        <PlusIcon className="mr-2 h-4 w-4" />
        Add Transaction
      </Button>
    </>
  );

  // Create date range indicator
  const dateRangeIndicator = (
    <div className="bg-muted/50 rounded-md p-2 text-sm text-muted-foreground flex items-center justify-between">
      <div className="flex items-center">
        <CalendarIcon className="h-4 w-4 mr-2" />
        {ignoreDate ? (
          <span>
            <span className="font-medium">Showing all data</span> (date filter
            disabled)
          </span>
        ) : dateRange.from && dateRange.to ? (
          <span>
            Showing data from{" "}
            <span className="font-medium">
              {format(dateRange.from, "MMMM d, yyyy")}
            </span>{" "}
            to{" "}
            <span className="font-medium">
              {format(dateRange.to, "MMMM d, yyyy")}
            </span>
          </span>
        ) : (
          <span>No date range selected</span>
        )}
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant={ignoreDate ? "default" : "outline"}
          size="sm"
          className="h-7 px-2 text-xs"
          onClick={() => setIgnoreDate(!ignoreDate)}
        >
          {ignoreDate ? "Enable Date Filter" : "Show All Data"}
        </Button>
        {!ignoreDate && (
          <Button
            variant="ghost"
            size="sm"
            className="h-7 px-2 text-xs"
            onClick={() => {
              // Reset to current month
              const today = new Date();
              const firstDayOfMonth = new Date(
                today.getFullYear(),
                today.getMonth(),
                1
              );
              const lastDayOfMonth = new Date(
                today.getFullYear(),
                today.getMonth() + 1,
                0
              );
              setDateRange({
                from: firstDayOfMonth,
                to: lastDayOfMonth,
              });
            }}
          >
            Reset to current month
          </Button>
        )}
      </div>
    </div>
  );

  // Create floating action button
  const floatingActionButton = (
    <Button
      onClick={() => setIsAddTransactionOpen(true)}
      size="icon"
      className="h-14 w-14 rounded-full shadow-lg"
    >
      <PlusIcon className="h-6 w-6" />
      <span className="sr-only">Add Transaction</span>
    </Button>
  );

  // Define tabs
  const tabs = [
    {
      value: "overview",
      label: "Overview",
      content: <DashboardOverview />,
    },
    {
      value: "transactions",
      label: "Transactions",
      content: <Transactions />,
    },
  ];

  return (
    <>
      <BasePage
        title="Dashboard"
        isLoading={loading}
        error={
          error
            ? "We couldn't load your financial data. Please try refreshing the page."
            : null
        }
        onRetry={refreshData}
        headerActions={headerActions}
        tabs={tabs}
        defaultTab="overview"
        floatingActionButton={floatingActionButton}
        loadingMessage="Loading your financial data..."
      >
        {dateRangeIndicator}
      </BasePage>

      <AddTransactionSheet
        open={isAddTransactionOpen}
        onOpenChange={setIsAddTransactionOpen}
        onSuccess={refreshData}
      />
    </>
  );
}

import {
  BarChart3Icon,
  CoinsIcon,
  LineChartIcon,
  TrendingUpIcon,
} from "lucide-react";
import { Investment, InvestmentStats } from "@/services/investment.service";
import {
  StatsGrid,
  PieChart,
  LineChart,
  formatCurrency,
  formatPercentage,
  useInvestmentTypeData,
  usePerformanceChartData,
} from "@/shared/components";
import { InvestmentPortfolio } from "./Portfolio";

export function InvestmentOverview({
  investmentStats,
  handleEditInvestment,
  handleDeleteInvestment,
}: {
  investmentStats?: InvestmentStats;
  handleEditInvestment?: (investment: Investment) => void;
  handleDeleteInvestment?: (investment: Investment) => void;
}) {
  // Process chart data using shared hooks
  const performanceChartData = usePerformanceChartData(
    investmentStats?.performanceData || []
  );
  const typeChartData = useInvestmentTypeData(
    investmentStats?.investmentsByType || []
  );

  // Create stats cards data
  const statsCards = [
    {
      title: "Total Investment",
      value: investmentStats?.totalInvestment || 0,
      icon: <CoinsIcon className="h-4 w-4 text-primary" />,
      formatter: formatCurrency,
      borderColor: "border-l-primary/70",
      iconBgColor: "bg-primary/10",
    },
    {
      title: "Current Value",
      value: investmentStats?.totalCurrentValue || 0,
      icon: <BarChart3Icon className="h-4 w-4 text-emerald-500" />,
      formatter: formatCurrency,
      borderColor: "border-l-emerald-500/70",
      iconBgColor: "bg-emerald-500/10",
    },
    {
      title: "Total Return",
      value: investmentStats?.totalReturn || 0,
      icon: <LineChartIcon className="h-4 w-4 text-blue-500" />,
      formatter: formatCurrency,
      borderColor: "border-l-blue-500/70",
      iconBgColor: "bg-blue-500/10",
    },
    {
      title: "Return %",
      value: investmentStats?.returnPercentage || 0,
      icon: <TrendingUpIcon className="h-4 w-4 text-amber-500" />,
      formatter: formatPercentage,
      borderColor: "border-l-amber-500/70",
      iconBgColor: "bg-amber-500/10",
    },
  ];

  // Performance chart lines configuration
  const performanceLines = [
    {
      dataKey: "value",
      stroke: "#8884d8",
      name: "Performance",
    },
  ];

  return (
    <>
      {/* Stats Cards */}
      <StatsGrid cards={statsCards} loading={!investmentStats} />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Performance Chart */}
        <div className="col-span-4">
          <LineChart
            title="Performance"
            description="Your investment performance over time"
            data={performanceChartData.data}
            lines={performanceLines}
            isLoading={!investmentStats}
            formatValue={formatCurrency}
            height={300}
          />
        </div>

        {/* Investment by Type Chart */}
        <div className="col-span-3">
          <PieChart
            title="Investment by Type"
            description="Your investment distribution across types"
            data={typeChartData.data}
            isLoading={!investmentStats}
            colors={typeChartData.colors}
            formatValue={formatCurrency}
            height={300}
          />
        </div>
      </div>

      {/* Recent Investments */}
      <div className="grid gap-4 md:grid-cols-1">
        <div className="col-span-1">
          {investmentStats && (
            <InvestmentPortfolio
              investments={investmentStats.recentInvestments.slice(0, 5)}
              isLoading={false}
              onEdit={handleEditInvestment}
              onDelete={handleDeleteInvestment}
              // title="Recent Investments"
              // description="Your most recent investment activities"
            />
          )}
        </div>
      </div>
    </>
  );
}

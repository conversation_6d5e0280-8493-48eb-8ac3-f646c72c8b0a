"use client";

import { useState, useEffect, useCallback } from "react";
import { PlusIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  InvestmentProvider,
  useInvestment,
} from "@/contexts/InvestmentContext";
import {
  Investment,
  INVESTMENT_TYPE,
  INVESTMENT_PURPOSE,
  StockDataType,
} from "@/services/investment.service";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteInvestment } from "@/services/investment.service";
import { toast } from "@/components/ui/use-toast";
import { getPriceTracking } from "@/services/investment.service";
import { BasePage } from "@/shared/components";
import { InvestmentOverview } from "./Components/Overview-Refactored";
import { Portfolio } from "./Components/Portfolio";
import { MarketData } from "./Components/MarketData";
import { AddInvestmentSheet } from "./Components/AddInvestment";

function InvestmentContent() {
  const [isAddInvestmentOpen, setIsAddInvestmentOpen] = useState(false);
  const [selectedInvestment, setSelectedInvestment] =
    useState<Investment | null>(null);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const [stocksData, setStocksData] = useState<StockDataType[]>([]);
  const [ownedStocksData, setOwnedStocksData] = useState<StockDataType[]>([]);
  const [monitoredStocksData, setMonitoredStocksData] = useState<
    StockDataType[]
  >([]);
  const [mutualFundsData, setMutualFundsData] = useState<StockDataType[]>([]);
  const [commoditiesData, setCommoditiesData] = useState<StockDataType[]>([]);
  const [stocksFilter, setStocksFilter] = useState<
    "all" | "owned" | "monitored"
  >("all");

  const [isLoadingStocks, setIsLoadingStocks] = useState(false);

  const { investmentStats, loading, error, refreshData } = useInvestment();

  const handleEditInvestment = (investment: Investment) => {
    setSelectedInvestment(investment);
    setIsAddInvestmentOpen(true);
  };

  const handleDeleteInvestment = (investment: Investment) => {
    setSelectedInvestment(investment);
    setIsDeleteDialogOpen(true);
  };

  // Create header actions
  const headerActions = (
    <Button
      onClick={() => setIsAddInvestmentOpen(true)}
      className="hidden md:block"
    >
      <PlusIcon className="mr-2 h-4 w-4" />
      Add Investment
    </Button>
  );

  // Create floating action button
  const floatingActionButton = (
    <Button
      onClick={() => setIsAddInvestmentOpen(true)}
      size="icon"
      className="h-14 w-14 rounded-full shadow-lg"
    >
      <PlusIcon className="h-6 w-6" />
      <span className="sr-only">Add Investment</span>
    </Button>
  );

  // Define tabs
  const tabs = [
    {
      value: "overview",
      label: "Overview",
      content: (
        <InvestmentOverview
          investmentStats={investmentStats}
          handleEditInvestment={handleEditInvestment}
          handleDeleteInvestment={handleDeleteInvestment}
        />
      ),
    },
    {
      value: "portfolio",
      label: "Portfolio",
      content: (
        <Portfolio
          investmentStats={investmentStats}
          handleEditInvestment={handleEditInvestment}
          handleDeleteInvestment={handleDeleteInvestment}
        />
      ),
    },
    {
      value: "market",
      label: "Market Data",
      content: (
        <MarketData
          isLoadingStocks={isLoadingStocks}
          commoditiesData={commoditiesData}
          stocksData={stocksData}
          ownedStocksData={ownedStocksData}
          monitoredStocksData={monitoredStocksData}
          mutualFundsData={mutualFundsData}
          stocksFilter={stocksFilter}
          setStocksFilter={setStocksFilter}
        />
      ),
    },
  ];

  const confirmDelete = async () => {
    if (!selectedInvestment?._id) return;

    try {
      await deleteInvestment(selectedInvestment._id);
      toast({
        title: "Investment deleted",
        description: "Your investment has been deleted successfully.",
      });
      refreshData();
    } catch (error) {
      console.error("Failed to delete investment:", error);
      toast({
        title: "Error",
        description: "Failed to delete investment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedInvestment(null);
    }
  };

  const fetchStockData = useCallback(async () => {
    try {
      setIsLoadingStocks(true);
      Promise.all(
        Object.keys(INVESTMENT_TYPE).map((type) =>
          getPriceTracking(type.toLowerCase())
            .then((data) => ({ type, data }))
            .catch((error) => {
              return { type, data: [], error };
            })
        )
      ).then((results) => {
        const tStocksData = [];
        const tOwnedStocksData = [];
        const tMonitoredStocksData = [];
        const tMutualFundsData = [];
        const tCommoditiesData = [];

        // Get the list of owned and monitored investments from investmentStats
        const ownedSymbols =
          investmentStats?.recentInvestments
            .filter(
              (inv) =>
                inv.purpose === INVESTMENT_PURPOSE.OWNED &&
                inv.type === INVESTMENT_TYPE.STOCK
            )
            .map((inv) => inv.symbol.toUpperCase()) || [];

        const monitoredSymbols =
          investmentStats?.recentInvestments
            .filter(
              (inv) =>
                inv.purpose === INVESTMENT_PURPOSE.MONITORING &&
                inv.type === INVESTMENT_TYPE.STOCK
            )
            .map((inv) => inv.symbol.toUpperCase()) || [];

        results.forEach(({ type, data }) => {
          if (type === INVESTMENT_TYPE.STOCK) {
            // Add purpose to each stock based on the symbol
            const stocksWithPurpose = data.map((stock: StockDataType) => {
              const stockSymbol = stock.name.split(" ")[0].toUpperCase();
              let purpose: INVESTMENT_PURPOSE | undefined;

              if (ownedSymbols.includes(stockSymbol)) {
                purpose = INVESTMENT_PURPOSE.OWNED;
              } else if (monitoredSymbols.includes(stockSymbol)) {
                purpose = INVESTMENT_PURPOSE.MONITORING;
              }

              return { ...stock, purpose };
            });

            tStocksData.push(...stocksWithPurpose);

            // Separate owned and monitored stocks
            stocksWithPurpose.forEach((stock) => {
              if (stock.purpose === INVESTMENT_PURPOSE.OWNED) {
                tOwnedStocksData.push(stock);
              } else if (stock.purpose === INVESTMENT_PURPOSE.MONITORING) {
                tMonitoredStocksData.push(stock);
              }
            });
          } else if (type === INVESTMENT_TYPE.MUTUAL_FUND) {
            tMutualFundsData.push(...data);
          } else {
            // Gold, Silver, Currency
            tCommoditiesData.push(...data);
          }
        });

        setStocksData(tStocksData);
        setOwnedStocksData(tOwnedStocksData);
        setMonitoredStocksData(tMonitoredStocksData);
        setMutualFundsData(tMutualFundsData);
        setCommoditiesData(tCommoditiesData);
      });
    } catch (error) {
      console.error("Failed to fetch stock data:", error);
    } finally {
      setIsLoadingStocks(false);
    }
  }, [investmentStats]);

  useEffect(() => {
    fetchStockData();
  }, [fetchStockData]);

  return (
    <>
      <BasePage
        title="Investments"
        isLoading={loading}
        error={
          error
            ? "We couldn't load your investment data. Please try refreshing the page."
            : null
        }
        onRetry={refreshData}
        headerActions={headerActions}
        tabs={tabs}
        defaultTab="overview"
        floatingActionButton={floatingActionButton}
        loadingMessage="Loading your investment data..."
      />

      <AddInvestmentSheet
        open={isAddInvestmentOpen}
        onOpenChange={(open) => {
          setIsAddInvestmentOpen(open);
          // Reset selectedInvestment when closing the sheet
          if (!open) {
            setSelectedInvestment(null);
          }
        }}
        onSuccess={refreshData}
        selectedInvestment={selectedInvestment}
      />

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the investment "
              {selectedInvestment?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default function InvestmentPage() {
  return (
    <InvestmentProvider>
      <InvestmentContent />
    </InvestmentProvider>
  );
}
